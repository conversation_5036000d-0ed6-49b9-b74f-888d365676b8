<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>为你画一颗心 ❤️</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #ffeef8 0%, #ffe0f0 25%, #f8e8ff 50%, #ffe8f5 75%, #fff0f8 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Georgia', '微软雅黑', serif;
            overflow: hidden;
        }

        .container {
            text-align: center;
            position: relative;
        }

        .title {
            font-size: 2.5rem;
            color: #d63384;
            margin-bottom: 30px;
            opacity: 0;
            animation: fadeInTitle 2s ease-in-out forwards;
            text-shadow: 2px 2px 4px rgba(214, 51, 132, 0.3);
            font-weight: 300;
            letter-spacing: 2px;
        }

        .heart-container {
            position: relative;
            width: 400px;
            height: 400px;
            margin: 0 auto;
        }

        .heart-svg {
            width: 100%;
            height: 100%;
        }

        .heart-path {
            fill: none;
            stroke: #e91e63;
            stroke-width: 4;
            stroke-linecap: round;
            stroke-linejoin: round;
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            filter: drop-shadow(0 0 10px rgba(233, 30, 99, 0.5));
            animation: drawHeart 4s ease-in-out forwards 1s;
        }

        .sparkle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: radial-gradient(circle, #ff69b4, #ff1493);
            border-radius: 50%;
            opacity: 0;
            animation: sparkleAnimation 2s infinite;
        }

        .sparkle:nth-child(1) { top: 20%; left: 30%; animation-delay: 2s; }
        .sparkle:nth-child(2) { top: 40%; left: 70%; animation-delay: 2.5s; }
        .sparkle:nth-child(3) { top: 60%; left: 20%; animation-delay: 3s; }
        .sparkle:nth-child(4) { top: 80%; left: 60%; animation-delay: 3.5s; }
        .sparkle:nth-child(5) { top: 30%; left: 80%; animation-delay: 4s; }
        .sparkle:nth-child(6) { top: 70%; left: 40%; animation-delay: 4.5s; }

        .message {
            font-size: 1.8rem;
            color: #c2185b;
            margin-top: 40px;
            opacity: 0;
            animation: fadeInMessage 2s ease-in-out forwards 5s;
            font-style: italic;
            text-shadow: 1px 1px 2px rgba(194, 24, 91, 0.3);
        }

        .floating-hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-heart {
            position: absolute;
            font-size: 20px;
            color: rgba(233, 30, 99, 0.6);
            animation: floatUp 6s infinite linear;
            opacity: 0;
        }

        .floating-heart:nth-child(1) { left: 10%; animation-delay: 6s; }
        .floating-heart:nth-child(2) { left: 20%; animation-delay: 6.5s; }
        .floating-heart:nth-child(3) { left: 30%; animation-delay: 7s; }
        .floating-heart:nth-child(4) { left: 40%; animation-delay: 7.5s; }
        .floating-heart:nth-child(5) { left: 50%; animation-delay: 8s; }
        .floating-heart:nth-child(6) { left: 60%; animation-delay: 8.5s; }
        .floating-heart:nth-child(7) { left: 70%; animation-delay: 9s; }
        .floating-heart:nth-child(8) { left: 80%; animation-delay: 9.5s; }
        .floating-heart:nth-child(9) { left: 90%; animation-delay: 10s; }

        @keyframes fadeInTitle {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes drawHeart {
            to {
                stroke-dashoffset: 0;
            }
        }

        @keyframes sparkleAnimation {
            0%, 100% {
                opacity: 0;
                transform: scale(0.5);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        @keyframes fadeInMessage {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes floatUp {
            0% {
                opacity: 0;
                transform: translateY(100vh) rotate(0deg);
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: translateY(-100px) rotate(360deg);
            }
        }

        .pulse-effect {
            animation: pulse 2s infinite ease-in-out 5s;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }
    </style>
</head>
<body>
    <div class="floating-hearts">
        <div class="floating-heart">💕</div>
        <div class="floating-heart">💖</div>
        <div class="floating-heart">💗</div>
        <div class="floating-heart">💝</div>
        <div class="floating-heart">💞</div>
        <div class="floating-heart">💕</div>
        <div class="floating-heart">💖</div>
        <div class="floating-heart">💗</div>
        <div class="floating-heart">💝</div>
    </div>

    <div class="container">
        <h1 class="title">为你画一颗心</h1>
        
        <div class="heart-container pulse-effect">
            <svg class="heart-svg" viewBox="0 0 400 400">
                <path class="heart-path" d="M200,320 C200,320 50,200 50,120 C50,80 80,50 120,50 C140,50 160,60 180,80 C180,80 180,80 200,100 C220,80 220,80 220,80 C240,60 260,50 280,50 C320,50 350,80 350,120 C350,200 200,320 200,320 Z"/>
            </svg>
            
            <div class="sparkle"></div>
            <div class="sparkle"></div>
            <div class="sparkle"></div>
            <div class="sparkle"></div>
            <div class="sparkle"></div>
            <div class="sparkle"></div>
        </div>
        
        <p class="message">每一笔都是我对你的爱 💕</p>
    </div>
</body>
</html>
