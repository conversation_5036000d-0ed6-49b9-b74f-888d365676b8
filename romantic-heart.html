<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>为你画一颗心 ❤️</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #ffeef8 0%, #ffe0f0 25%, #f8e8ff 50%, #ffe8f5 75%, #fff0f8 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Georgia', '微软雅黑', serif;
            overflow: hidden;
            position: relative;
        }

        /* 背景光斑效果 */
        .light-orbs {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .light-orb {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.3), rgba(255, 182, 193, 0.1), transparent);
            animation: floatOrb 8s infinite ease-in-out;
        }

        .light-orb:nth-child(1) {
            width: 120px;
            height: 120px;
            top: 10%;
            left: 20%;
            animation-delay: 0s;
        }

        .light-orb:nth-child(2) {
            width: 80px;
            height: 80px;
            top: 60%;
            left: 70%;
            animation-delay: 2s;
        }

        .light-orb:nth-child(3) {
            width: 100px;
            height: 100px;
            top: 30%;
            left: 80%;
            animation-delay: 4s;
        }

        .container {
            text-align: center;
            position: relative;
        }

        .title {
            font-size: 2.5rem;
            color: #d63384;
            margin-bottom: 30px;
            opacity: 0;
            animation: fadeInTitle 2s ease-in-out forwards;
            text-shadow: 2px 2px 4px rgba(214, 51, 132, 0.3);
            font-weight: 300;
            letter-spacing: 2px;
        }

        .heart-container {
            position: relative;
            width: 400px;
            height: 400px;
            margin: 0 auto;
        }

        .heart-svg {
            width: 100%;
            height: 100%;
        }

        .heart-path {
            fill: url(#heartGradient);
            fill-opacity: 0;
            stroke: url(#strokeGradient);
            stroke-width: 4;
            stroke-linecap: round;
            stroke-linejoin: round;
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            filter: drop-shadow(0 0 20px rgba(233, 30, 99, 0.8)) drop-shadow(0 0 40px rgba(255, 105, 180, 0.4));
            animation: drawHeart 4s ease-in-out forwards 1s, fillHeart 2s ease-in-out forwards 5s;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .heart-path:hover {
            filter: drop-shadow(0 0 25px rgba(233, 30, 99, 1)) drop-shadow(0 0 50px rgba(255, 105, 180, 0.6));
            transform: scale(1.02);
        }

        /* 爱心光环效果 */
        .heart-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 420px;
            height: 420px;
            transform: translate(-50%, -50%);
            border: 2px solid rgba(255, 105, 180, 0.3);
            border-radius: 50%;
            opacity: 0;
            animation: glowRing 3s ease-in-out infinite 6s;
        }

        .heart-glow:nth-child(2) {
            width: 450px;
            height: 450px;
            animation-delay: 6.5s;
            border-color: rgba(233, 30, 99, 0.2);
        }

        /* 多种粒子效果 */
        .particle {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }

        .sparkle {
            width: 8px;
            height: 8px;
            background: radial-gradient(circle, #fff, #ff69b4);
            border-radius: 50%;
            animation: sparkleAnimation 3s infinite;
        }

        .heart-particle {
            font-size: 12px;
            color: rgba(255, 105, 180, 0.8);
            animation: heartParticleAnimation 4s infinite;
        }

        .diamond {
            width: 10px;
            height: 10px;
            background: linear-gradient(45deg, #fff, #ff69b4, #fff);
            transform: rotate(45deg);
            animation: diamondAnimation 3.5s infinite;
        }

        .star {
            font-size: 14px;
            color: #ffd700;
            animation: starAnimation 2.5s infinite;
        }

        .particle:nth-child(1) { top: 15%; left: 25%; animation-delay: 2s; }
        .particle:nth-child(2) { top: 35%; left: 75%; animation-delay: 2.5s; }
        .particle:nth-child(3) { top: 55%; left: 15%; animation-delay: 3s; }
        .particle:nth-child(4) { top: 75%; left: 65%; animation-delay: 3.5s; }
        .particle:nth-child(5) { top: 25%; left: 85%; animation-delay: 4s; }
        .particle:nth-child(6) { top: 65%; left: 35%; animation-delay: 4.5s; }
        .particle:nth-child(7) { top: 45%; left: 10%; animation-delay: 5s; }
        .particle:nth-child(8) { top: 85%; left: 80%; animation-delay: 5.5s; }

        .message {
            font-size: 1.8rem;
            color: #c2185b;
            margin-top: 40px;
            opacity: 0;
            animation: fadeInMessage 2s ease-in-out forwards 5s;
            font-style: italic;
            text-shadow: 1px 1px 2px rgba(194, 24, 91, 0.3);
        }

        .floating-hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-heart {
            position: absolute;
            font-size: 20px;
            color: rgba(233, 30, 99, 0.6);
            animation: floatUp 6s infinite linear;
            opacity: 0;
        }

        .floating-heart:nth-child(1) { left: 10%; animation-delay: 6s; }
        .floating-heart:nth-child(2) { left: 20%; animation-delay: 6.5s; }
        .floating-heart:nth-child(3) { left: 30%; animation-delay: 7s; }
        .floating-heart:nth-child(4) { left: 40%; animation-delay: 7.5s; }
        .floating-heart:nth-child(5) { left: 50%; animation-delay: 8s; }
        .floating-heart:nth-child(6) { left: 60%; animation-delay: 8.5s; }
        .floating-heart:nth-child(7) { left: 70%; animation-delay: 9s; }
        .floating-heart:nth-child(8) { left: 80%; animation-delay: 9.5s; }
        .floating-heart:nth-child(9) { left: 90%; animation-delay: 10s; }

        /* 玫瑰花瓣飘落效果 */
        .rose-petals {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .rose-petal {
            position: absolute;
            width: 12px;
            height: 8px;
            background: radial-gradient(ellipse, #ff69b4, #ff1493);
            border-radius: 50% 10% 50% 10%;
            opacity: 0;
            animation: petalFall 8s infinite linear;
        }

        .rose-petal:nth-child(1) { left: 10%; animation-delay: 7s; }
        .rose-petal:nth-child(2) { left: 20%; animation-delay: 7.5s; }
        .rose-petal:nth-child(3) { left: 30%; animation-delay: 8s; }
        .rose-petal:nth-child(4) { left: 40%; animation-delay: 8.5s; }
        .rose-petal:nth-child(5) { left: 50%; animation-delay: 9s; }
        .rose-petal:nth-child(6) { left: 60%; animation-delay: 9.5s; }
        .rose-petal:nth-child(7) { left: 70%; animation-delay: 10s; }
        .rose-petal:nth-child(8) { left: 80%; animation-delay: 10.5s; }
        .rose-petal:nth-child(9) { left: 90%; animation-delay: 11s; }

        @keyframes fadeInTitle {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes drawHeart {
            to {
                stroke-dashoffset: 0;
            }
        }

        @keyframes fillHeart {
            from {
                fill-opacity: 0;
            }
            to {
                fill-opacity: 1;
            }
        }

        @keyframes sparkleAnimation {
            0%, 100% {
                opacity: 0;
                transform: scale(0.5);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        @keyframes fadeInMessage {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes floatUp {
            0% {
                opacity: 0;
                transform: translateY(100vh) rotate(0deg);
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: translateY(-100px) rotate(360deg);
            }
        }

        .pulse-effect {
            animation: pulse 2s infinite ease-in-out 5s;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes floatOrb {
            0%, 100% {
                transform: translateY(0px) translateX(0px);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-20px) translateX(10px);
                opacity: 0.6;
            }
        }

        @keyframes glowRing {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            50% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1.1);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(1.3);
            }
        }

        @keyframes sparkleAnimation {
            0%, 100% {
                opacity: 0;
                transform: scale(0.5) rotate(0deg);
            }
            50% {
                opacity: 1;
                transform: scale(1.2) rotate(180deg);
            }
        }

        @keyframes heartParticleAnimation {
            0%, 100% {
                opacity: 0;
                transform: scale(0.8) translateY(0px);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.1) translateY(-10px);
            }
        }

        @keyframes diamondAnimation {
            0%, 100% {
                opacity: 0;
                transform: rotate(45deg) scale(0.7);
            }
            50% {
                opacity: 1;
                transform: rotate(225deg) scale(1.3);
            }
        }

        @keyframes starAnimation {
            0%, 100% {
                opacity: 0;
                transform: scale(0.6) rotate(0deg);
            }
            50% {
                opacity: 1;
                transform: scale(1.4) rotate(360deg);
            }
        }

        @keyframes petalFall {
            0% {
                opacity: 0;
                transform: translateY(-100px) rotate(0deg);
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: translateY(100vh) rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <!-- 背景光斑 -->
    <div class="light-orbs">
        <div class="light-orb"></div>
        <div class="light-orb"></div>
        <div class="light-orb"></div>
    </div>

    <!-- 玫瑰花瓣 -->
    <div class="rose-petals">
        <div class="rose-petal"></div>
        <div class="rose-petal"></div>
        <div class="rose-petal"></div>
        <div class="rose-petal"></div>
        <div class="rose-petal"></div>
        <div class="rose-petal"></div>
        <div class="rose-petal"></div>
        <div class="rose-petal"></div>
        <div class="rose-petal"></div>
    </div>

    <div class="floating-hearts">
        <div class="floating-heart">💕</div>
        <div class="floating-heart">💖</div>
        <div class="floating-heart">💗</div>
        <div class="floating-heart">💝</div>
        <div class="floating-heart">💞</div>
        <div class="floating-heart">💕</div>
        <div class="floating-heart">💖</div>
        <div class="floating-heart">💗</div>
        <div class="floating-heart">💝</div>
    </div>

    <div class="container">
        <h1 class="title">为你画一颗心</h1>
        
        <div class="heart-container pulse-effect" onclick="restartAnimation()">
            <!-- 光环效果 -->
            <div class="heart-glow"></div>
            <div class="heart-glow"></div>

            <svg class="heart-svg" viewBox="0 0 400 400">
                <defs>
                    <!-- 主要填充渐变 -->
                    <radialGradient id="heartGradient" cx="50%" cy="40%" r="60%">
                        <stop offset="0%" style="stop-color:#fff;stop-opacity:0.9" />
                        <stop offset="20%" style="stop-color:#ff69b4;stop-opacity:1" />
                        <stop offset="60%" style="stop-color:#e91e63;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#8e0e3e;stop-opacity:1" />
                    </radialGradient>

                    <!-- 描边渐变 -->
                    <linearGradient id="strokeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ff1493;stop-opacity:1" />
                        <stop offset="50%" style="stop-color:#e91e63;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#c2185b;stop-opacity:1" />
                    </linearGradient>

                    <!-- 珠光效果 -->
                    <linearGradient id="pearlGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:rgba(255,255,255,0.8);stop-opacity:1" />
                        <stop offset="50%" style="stop-color:rgba(255,255,255,0.3);stop-opacity:1" />
                        <stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:1" />
                    </linearGradient>
                </defs>
                <path class="heart-path" d="M200,320 C200,320 50,200 50,120 C50,80 80,50 120,50 C140,50 160,60 180,80 C180,80 180,80 200,100 C220,80 220,80 220,80 C240,60 260,50 280,50 C320,50 350,80 350,120 C350,200 200,320 200,320 Z"/>
                <!-- 珠光覆盖层 -->
                <path class="heart-path" d="M200,320 C200,320 50,200 50,120 C50,80 80,50 120,50 C140,50 160,60 180,80 C180,80 180,80 200,100 C220,80 220,80 220,80 C240,60 260,50 280,50 C320,50 350,80 350,120 C350,200 200,320 200,320 Z" fill="url(#pearlGradient)" stroke="none" opacity="0.4"/>
            </svg>

            <!-- 多种粒子效果 -->
            <div class="particle sparkle"></div>
            <div class="particle heart-particle">💕</div>
            <div class="particle diamond"></div>
            <div class="particle star">✨</div>
            <div class="particle sparkle"></div>
            <div class="particle heart-particle">💖</div>
            <div class="particle diamond"></div>
            <div class="particle star">⭐</div>
        </div>
        
        <p class="message">每一笔都是我对你的爱 💕</p>
    </div>
</body>
</html>
